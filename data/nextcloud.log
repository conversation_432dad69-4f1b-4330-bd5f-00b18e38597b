{"reqId": "nCURO12mfLOhWcDLmePr", "level": 3, "time": "2025-07-21T11:09:44+00:00", "remoteAddr": "127.0.0.1", "user": false, "app": "PHP", "method": "POST", "url": "/index.php/apps/cirrusconnect/webhook", "message": "Allowed memory size of 134217728 bytes exhausted (tried to allocate 65691648 bytes) at /var/www/html/juuvasafe/apps-extra/cirrusconnect/lib/Service/WebhookDispatcher.php#19", "userAgent": "--", "version": "31.0.3.2", "data": {"app": "PHP"}}