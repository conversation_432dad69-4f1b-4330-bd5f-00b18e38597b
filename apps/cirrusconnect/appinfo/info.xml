<?xml version="1.0"?>
<info xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	  xsi:noNamespaceSchemaLocation="https://apps.nextcloud.com/schema/apps/info.xsd">
	<id>cirrusconnect</id>
	<name>Cirrus Connect</name>
	<summary>A lightweight JuuvaSafe app that securely handles webhook events to automate user onboarding.</summary>
	<description>A lightweight JuuvaSafe app designed to securely process webhook events for user onboarding. When triggered, the app automatically creates a JuuvaSafe user using the provided email, sets a temporary password, and sends the onboarding credentials via email.</description>
	<version>1.0.0</version>
	<licence>agpl</licence>
	<author mail="<EMAIL>" homepage="https://amentotech.com/">Amentotech</author>
	<namespace>CirrusConnect</namespace>
	<category>tools</category>
	<bugs>https://amentotech.com/</bugs>
	<dependencies>
		<nextcloud min-version="31" max-version="31"/>
	</dependencies>
	<navigations>
		<navigation>
			<name>My subscription</name>
			<route>cirrusconnect.subscriptions.index</route>
			<order>5</order>
			<type>settings</type>
			<icon>/apps-extra/cirrusconnect/img/subscription.svg</icon>
		</navigation>
	</navigations>
</info>
