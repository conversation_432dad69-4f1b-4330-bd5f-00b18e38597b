<?php

namespace OCA\CirrusConnect\Service\Handlers;

use OCA\CirrusConnect\Service\WebhookService;
use OCP\AppFramework\Http;
use OCP\AppFramework\Http\DataResponse;
use function OCP\Log\logger;

class ResendInviteEmailHandler
{
    private $webhookService;

    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }


    public function handle(array $payload): DataResponse
    {

        $email = $this->webhookService->getEmail($payload);
        if (empty($email)) {
            return new DataResponse(['status' => 'error', 'message' => 'Invalid data'], HTTP::STATUS_BAD_REQUEST);
        }

        $firstName = $this->webhookService->getFirstName($payload);
        if (empty($firstName)) {
            return new DataResponse(['status' => 'error', 'message' => 'Invalid data'], HTTP::STATUS_BAD_REQUEST);
        }

        $lastName = $this->webhookService->getLastName($payload);
        if (empty($lastName)) {
            return new DataResponse(['status' => 'error', 'message' => 'Invalid data'], HTTP::STATUS_BAD_REQUEST);
        }

        if ($this->webhookService->emailExists($email)) {
            logger('cirrusconnect')->info('Webhook ignored: user already exists', ['email' => $email]);
            return new DataResponse(['status' => 'error', 'message' => 'User already exists'], HTTP::STATUS_UNPROCESSABLE_ENTITY);
        }

        $billingInformation = $this->webhookService->getBillingInformation($payload);
        if (empty($billingInformation) || !is_array($billingInformation)) {
            return new DataResponse(['status' => 'error', 'message' => 'Invalid data'], HTTP::STATUS_BAD_REQUEST);
        }

        $subscriptions = $this->webhookService->getSubscriptions($payload);
        if (empty($subscriptions) || !is_array($subscriptions)) {
            return new DataResponse(['status' => 'error', 'message' => 'Invalid data'], HTTP::STATUS_BAD_REQUEST);
        }

        $cirrusUserId = $this->webhookService->getCirrusUserId($payload);

        $username = $this->webhookService->getUsername($email);
        $fullName = $this->webhookService->getFullName($firstName, $lastName);

        try {

            $user     = $this->webhookService->createUser($username, $email, $fullName, $billingInformation, $subscriptions, $cirrusUserId);

            $this->webhookService->saveUserPreferences($user->getUID(), 'create_user_payload', json_encode($payload));

            return new DataResponse([
                'status'     => 'success',
                'message'     => 'User created',
                'user_id'     => $user->getUID(),
            ], HTTP::STATUS_OK);
        } catch (\Throwable $th) {
            logger('cirrusconnect')->error('Webhook error', ['error' => $th->getMessage()]);
            return new DataResponse(['status' => 'error', 'message' => 'Invalid data'], HTTP::STATUS_BAD_REQUEST);
        }
    }
}
