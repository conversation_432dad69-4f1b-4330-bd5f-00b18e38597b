<?php
style('cirrusconnect', 'style');
?>

<div id="app-navigation">
    <ul class="app-navigation-list cirrusconnect-navigation__list">
        <li class="active app-navigation-entry-wrapper cirrusconnect-navigation__item">
            <a href="<?php p(\OC::$server->getURLGenerator()->linkToRoute('cirrusconnect.subscriptions.index')); ?>">
                <img src="<?php p(\OC::$server->getURLGenerator()->imagePath('cirrusconnect', 'subscription-white.svg')); ?>" alt="">
                <?php p($l->t('My subscription')); ?>
            </a>
        </li>
    </ul>
</div>

<div id="app-content">
    <div id="cirrusconnect-content">
        <h2><?php p($l->t('My subscription')); ?></h2>

        <div class="subscription-cards">
            <?php foreach ($_['subscriptions'] as $id => $subscription): ?>
                <div class="subscription-card">
                    <div class="subscription-header">
                        <h3><?php p($subscription['name'] ?? null); ?></h3>
                        <?php if (isset($subscription['status'])): ?>
                            <span class="subscription-status status-<?php p(strtolower($subscription['status'])); ?>">
                                <?php p(ucwords($subscription['status'])); ?>
                            </span>
                        <?php endif; ?>
                    </div>

                    <div class="subscription-details">
                        <?php if (!empty($subscription['period']) && !empty($subscription['period']['start']) && !empty($subscription['period']['end'])): ?>
                            <div class="subscription-period">
                                <span class="label"><?php p($l->t('Period')); ?>:</span>
                                <span class="value">
                                    <?php p(date('M d, Y', strtotime($subscription['period']['start']))); ?> -
                                    <?php p(date('M d, Y', strtotime($subscription['period']['end']))); ?>
                                </span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($subscription['nextBillingDate'])): ?>
                            <div class="subscription-next-billing">
                                <span class="label"><?php p($l->t('Next Billing Date')); ?>:</span>
                                <span class="value">
                                    <?php p(date('M d, Y', strtotime($subscription['nextBillingDate']))); ?>
                                </span>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($subscription['price'])): ?>
                            <div class="subscription-price">
                                <span class="label"><?php p($l->t('Price')); ?>:</span>
                                <span class="value">
                                    <?php p('$' . $subscription['price']['amount']); ?>
                                    <?php if (isset($subscription['price']['interval'])): ?>
                                        / <?php p($subscription['price']['interval']); ?>
                                    <?php endif; ?>
                                </span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($subscription['autoRenew'])): ?>
                            <div class="subscription-renewal">
                                <span class="label"><?php p($l->t('Auto Renew')); ?>:</span>
                                <span class="value">
                                    <?php p($subscription['autoRenew'] ? $l->t('Yes') : $l->t('No')); ?>
                                </span>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($subscription['features']) && is_array($subscription['features']) && !empty($subscription['features'])): ?>
                            <div class="subscription-features">
                                <span class="label"><?php p($l->t('Features')); ?>:</span>
                                <ul>
                                    <?php foreach ($subscription['features'] as $feature): ?>
                                        <li><?php p($feature); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($subscription['purchaseDate'])): ?>
                            <div class="subscription-next-billing">
                                <span class="label"><?php p($l->t('Purchase Date')); ?>:</span>
                                <span class="value">
                                    <?php p(date('M d, Y', strtotime($subscription['purchaseDate']))); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($subscription['manage_subscription_link'])): ?>
                        <div class="subscription-actions">
                            <a target="_blank" href="<?php p($subscription['manage_subscription_link']) ?>" class="button primary manage-subscription"
                                data-subscription-id="<?php p($id); ?>">
                                <?php p($l->t('Manage Subscription')); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>

            <?php if (empty($_['subscriptions'])): ?>
                <div class="empty-state">
                    <div class="icon-subscription"></div>
                    <h3><?php p($l->t('No subscriptions found')); ?></h3>
                    <p><?php p($l->t('You don\'t have any active subscriptions yet.')); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>