{"reqId":"y196AwNV6Xpmj1ktTEhL","level":3,"time":"2025-07-07T15:10:39+00:00","remoteAddr":"127.0.0.1","user":"isa","app":"PHP","method":"GET","url":"/index.php/apps/cirrusconnect/my-subscription","message":"Undefined array key \"translations\" at /var/www/html/juuvasafe/lib/private/L10N/L10N.php#222","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"********","data":{"app":"PHP"}}
{"reqId":"y196AwNV6Xpmj1ktTEhL","level":3,"time":"2025-07-07T15:10:39+00:00","remoteAddr":"127.0.0.1","user":"isa","app":"index","method":"GET","url":"/index.php/apps/cirrusconnect/my-subscription","message":"array_merge(): Argument #2 must be of type array, null given","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"********","exception":{"Exception":"TypeError","Message":"array_merge(): Argument #2 must be of type array, null given","Code":0,"Trace":[{"file":"/var/www/html/juuvasafe/lib/private/L10N/L10N.php","line":222,"function":"array_merge"},{"file":"/var/www/html/juuvasafe/lib/private/L10N/L10N.php","line":50,"function":"load","class":"OC\\L10N\\L10N","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/L10N/Factory.php","line":120,"function":"__construct","class":"OC\\L10N\\L10N","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/L10N/LazyL10N.php","line":27,"function":"OC\\L10N\\{closure}","class":"OC\\L10N\\Factory","type":"->","args":["*** sensitive parameters replaced ***"]},{"file":"/var/www/html/juuvasafe/lib/private/L10N/LazyL10N.php","line":34,"function":"getL","class":"OC\\L10N\\LazyL10N","type":"->"},{"file":"/var/www/html/juuvasafe/apps-extra/cirrusconnect/templates/subscriptions.php","line":10,"function":"t","class":"OC\\L10N\\LazyL10N","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/Template/Base.php","line":161,"args":["/var/www/html/juuvasafe/apps-extra/cirrusconnect/templates/subscriptions.php"],"function":"include"},{"file":"/var/www/html/juuvasafe/lib/private/Template/Base.php","line":131,"function":"load","class":"OC\\Template\\Base","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/legacy/OC_Template.php","line":117,"function":"fetchPage","class":"OC\\Template\\Base","type":"->"},{"file":"/var/www/html/juuvasafe/lib/public/AppFramework/Http/TemplateResponse.php","line":189,"function":"fetchPage","class":"OC_Template","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/AppFramework/Http/Dispatcher.php","line":159,"function":"render","class":"OCP\\AppFramework\\Http\\TemplateResponse","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/AppFramework/App.php","line":161,"function":"dispatch","class":"OC\\AppFramework\\Http\\Dispatcher","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/Route/Router.php","line":307,"function":"main","class":"OC\\AppFramework\\App","type":"::"},{"file":"/var/www/html/juuvasafe/lib/base.php","line":1025,"function":"match","class":"OC\\Route\\Router","type":"->"},{"file":"/var/www/html/juuvasafe/index.php","line":24,"function":"handleRequest","class":"OC","type":"::"}],"File":"/var/www/html/juuvasafe/lib/private/L10N/L10N.php","Line":222,"message":"array_merge(): Argument #2 must be of type array, null given","exception":{},"CustomMessage":"array_merge(): Argument #2 must be of type array, null given"}}
{"reqId":"6Z5h2KFSAj91AmtqqXP9","level":3,"time":"2025-07-07T15:10:56+00:00","remoteAddr":"127.0.0.1","user":"isa","app":"PHP","method":"GET","url":"/index.php/apps/cirrusconnect/my-subscription","message":"Undefined array key \"translations\" at /var/www/html/juuvasafe/lib/private/L10N/L10N.php#222","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"********","data":{"app":"PHP"}}
{"reqId":"6Z5h2KFSAj91AmtqqXP9","level":3,"time":"2025-07-07T15:10:56+00:00","remoteAddr":"127.0.0.1","user":"isa","app":"index","method":"GET","url":"/index.php/apps/cirrusconnect/my-subscription","message":"array_merge(): Argument #2 must be of type array, null given","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"********","exception":{"Exception":"TypeError","Message":"array_merge(): Argument #2 must be of type array, null given","Code":0,"Trace":[{"file":"/var/www/html/juuvasafe/lib/private/L10N/L10N.php","line":222,"function":"array_merge"},{"file":"/var/www/html/juuvasafe/lib/private/L10N/L10N.php","line":50,"function":"load","class":"OC\\L10N\\L10N","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/L10N/Factory.php","line":120,"function":"__construct","class":"OC\\L10N\\L10N","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/L10N/LazyL10N.php","line":27,"function":"OC\\L10N\\{closure}","class":"OC\\L10N\\Factory","type":"->","args":["*** sensitive parameters replaced ***"]},{"file":"/var/www/html/juuvasafe/lib/private/L10N/LazyL10N.php","line":34,"function":"getL","class":"OC\\L10N\\LazyL10N","type":"->"},{"file":"/var/www/html/juuvasafe/apps-extra/cirrusconnect/templates/subscriptions.php","line":10,"function":"t","class":"OC\\L10N\\LazyL10N","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/Template/Base.php","line":161,"args":["/var/www/html/juuvasafe/apps-extra/cirrusconnect/templates/subscriptions.php"],"function":"include"},{"file":"/var/www/html/juuvasafe/lib/private/Template/Base.php","line":131,"function":"load","class":"OC\\Template\\Base","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/legacy/OC_Template.php","line":117,"function":"fetchPage","class":"OC\\Template\\Base","type":"->"},{"file":"/var/www/html/juuvasafe/lib/public/AppFramework/Http/TemplateResponse.php","line":189,"function":"fetchPage","class":"OC_Template","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/AppFramework/Http/Dispatcher.php","line":159,"function":"render","class":"OCP\\AppFramework\\Http\\TemplateResponse","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/AppFramework/App.php","line":161,"function":"dispatch","class":"OC\\AppFramework\\Http\\Dispatcher","type":"->"},{"file":"/var/www/html/juuvasafe/lib/private/Route/Router.php","line":307,"function":"main","class":"OC\\AppFramework\\App","type":"::"},{"file":"/var/www/html/juuvasafe/lib/base.php","line":1025,"function":"match","class":"OC\\Route\\Router","type":"->"},{"file":"/var/www/html/juuvasafe/index.php","line":24,"function":"handleRequest","class":"OC","type":"::"}],"File":"/var/www/html/juuvasafe/lib/private/L10N/L10N.php","Line":222,"message":"array_merge(): Argument #2 must be of type array, null given","exception":{},"CustomMessage":"array_merge(): Argument #2 must be of type array, null given"}}
